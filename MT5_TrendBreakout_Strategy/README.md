# MT5 趋势突破反转策略 EA

一个功能完整的MT5自动交易专家顾问，结合趋势突破和反转信号，专注于多头交易策略。

## 🎯 策略概述

该EA采用双重交易策略：

### 📈 突破做多策略
- **条件**：上升趋势 + 价格突破前高 + 成交量放大
- **入场**：价格突破20根K线最高点
- **确认**：EMA20 > EMA50 且均线向上倾斜

### 🔄 反转做多策略
- **条件**：非上升趋势 + RSI超卖 + 锤子线形态
- **入场**：RSI < 30 + 价格接近EMA50 + 可选RSI背离
- **确认**：锤子线K线形态

## ✨ 核心功能

### 🛡️ 风险管理系统
- **动态仓位计算**：基于账户权益和风险比例
- **智能止损止盈**：ATR动态计算 + 可配置盈亏比
- **追踪止损**：可选的动态追踪止损功能
- **多单管理**：可配置最大持仓数量

### 🎛️ 高级过滤系统
- **均线过滤**：可选的高时间框架均线过滤
- **时间过滤**：精确的交易时间控制（星期+时间段）
- **信号冷却**：防止重复开单的时间间隔控制

### 📊 实时监控仪表盘
- **策略状态**：实时显示突破和反转信号状态
- **仓位信息**：持仓数量、浮动盈亏、风险金额
- **技术指标**：RSI、EMA、成交量等关键数据
- **智能更新**：减少闪烁的优化显示机制

## 🔧 参数配置

### 📈 指标参数
```
EMA20_Period = 20        // EMA20周期
EMA50_Period = 50        // EMA50周期
RSI_Period = 14          // RSI周期
Volume_MA_Period = 20    // 成交量均线周期
ATR_Period = 14          // ATR周期
```

### 🛡️ 风险管理
```
Risk_Percent = 2.0              // 风险比例(%)
Profit_Ratio = 2.0              // 盈亏比
Enable_Trailing_Stop = true     // 启用追踪止损
Trailing_Start_Points = 100     // 追踪止损启动点数(1美金)
Trailing_Stop_Points = 50       // 追踪止损距离点数(0.5美金)
Trailing_Step_Points = 10       // 追踪止损步长点数(0.1美金)
```

### 🎯 交易过滤
```
Enable_MA_Filter = true          // 启用均线过滤
MA_Filter_Timeframe = PERIOD_H1  // 均线过滤时间周期
MA_Filter_Period = 20            // 均线过滤周期
MA_Filter_Method = MODE_EMA      // 均线计算方法
```

### ⏰ 时间过滤
```
Enable_Time_Filter = false       // 启用时间过滤
Trade_Monday = true             // 周一交易
Trade_Tuesday = true            // 周二交易
...                            // 其他星期设置
Start_Time = "00:00"           // 开始交易时间
End_Time = "23:59"             // 结束交易时间
```

### 📊 仓位管理
```
Max_Long_Positions = 3          // 最大多头持仓数
Magic_Number = 123456           // 魔术数字
```

## 🚀 安装使用

### 1. 安装步骤
1. 将 `TrendBreakoutReversal_EA.mq5` 复制到 `MT5/MQL5/Experts/` 目录
2. 在MT5中按 `F7` 或点击"编译"按钮编译EA
3. 将EA拖拽到XAUUSD图表上（推荐M3时间框架）
4. 根据需要调整参数设置

### 2. 推荐设置

#### 🥇 保守型设置
```
Risk_Percent = 1.0
Max_Long_Positions = 1
Trailing_Start_Points = 200
Enable_MA_Filter = true
```

#### ⚡ 激进型设置
```
Risk_Percent = 3.0
Max_Long_Positions = 5
Trailing_Start_Points = 50
Enable_Time_Filter = true
```

#### 🎯 平衡型设置（推荐）
```
Risk_Percent = 2.0
Max_Long_Positions = 3
Trailing_Start_Points = 100
Enable_MA_Filter = true
```

## 📋 重要说明

### ✅ 适用品种
- **主要适用**：XAUUSD（黄金）
- **时间框架**：M3（3分钟图）
- **最小手数**：0.01手

### ⚠️ 风险提醒
- **强制最小手数**：当计算手数小于0.01时，会强制使用0.01手开仓
- **追踪止损**：启用后不设置固定止盈，完全依赖追踪止损
- **均线过滤**：只有价格在指定均线之上才允许突破做多

### 🔍 调试功能
- 开启 `Debug_Mode = true` 可查看详细的交易决策过程
- 在MT5的"专家"标签页查看日志信息
- 仪表盘实时显示所有关键指标状态

## 📊 性能优化

### 🎨 仪表盘优化
- 智能更新机制，减少闪烁
- 不透明背景，清晰显示
- 分类布局，信息一目了然

### ⚡ 执行优化
- 并行数组管理追踪止损
- 缓存机制减少重复计算
- 严格的更新阈值控制

## 🛠️ 技术特性

- **编程语言**：MQL5
- **兼容版本**：MT5 Build 3000+
- **内存管理**：自动资源释放
- **错误处理**：完整的异常处理机制

## ⚖️ 免责声明

本EA仅供学习和研究使用。外汇和差价合约交易具有高风险，可能导致资金损失。使用者应：

1. 在模拟账户中充分测试
2. 根据个人风险承受能力调整参数
3. 定期监控EA运行状态
4. 自行承担所有交易风险

**请谨慎交易，理性投资！**
