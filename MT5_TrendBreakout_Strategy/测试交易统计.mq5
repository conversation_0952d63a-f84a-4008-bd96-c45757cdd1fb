//+------------------------------------------------------------------+
//|                                                测试交易统计.mq5 |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"
#property script_show_inputs

// 输入参数
input int Magic_Number = 123456;  // 魔术数字（与EA相同）

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 开始测试交易统计功能 ===");
    
    // 测试历史交易统计
    TestTradeStatistics();
    
    Print("=== 交易统计测试完成 ===");
}

//+------------------------------------------------------------------+
//| 测试交易统计功能                                                  |
//+------------------------------------------------------------------+
void TestTradeStatistics()
{
    // 交易统计结构
    struct TradeStatistics {
        int total_trades;
        int winning_trades;
        int losing_trades;
        double win_rate;
        double net_profit;
        
        int breakout_total_trades;
        int breakout_winning_trades;
        int breakout_losing_trades;
        double breakout_net_profit;
        
        int reversal_total_trades;
        int reversal_winning_trades;
        int reversal_losing_trades;
        double reversal_net_profit;
    } trade_stats;
    
    // 初始化统计数据
    trade_stats.total_trades = 0;
    trade_stats.winning_trades = 0;
    trade_stats.losing_trades = 0;
    trade_stats.win_rate = 0;
    trade_stats.net_profit = 0;
    
    trade_stats.breakout_total_trades = 0;
    trade_stats.breakout_winning_trades = 0;
    trade_stats.breakout_losing_trades = 0;
    trade_stats.breakout_net_profit = 0;
    
    trade_stats.reversal_total_trades = 0;
    trade_stats.reversal_winning_trades = 0;
    trade_stats.reversal_losing_trades = 0;
    trade_stats.reversal_net_profit = 0;
    
    // 获取历史交易数据
    if(!HistorySelect(0, TimeCurrent())) {
        Print("无法获取历史交易数据");
        return;
    }
    
    Print("历史交易记录总数: ", HistoryDealsTotal());
    
    // 统计历史交易
    for(int i = 0; i < HistoryDealsTotal(); i++) {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if(deal_ticket > 0) {
            string symbol = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
            long magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);
            long entry = HistoryDealGetInteger(deal_ticket, DEAL_ENTRY);
            
            if(symbol == Symbol() && magic == Magic_Number && entry == DEAL_ENTRY_OUT) {
                
                double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT) +
                                   HistoryDealGetDouble(deal_ticket, DEAL_SWAP) +
                                   HistoryDealGetDouble(deal_ticket, DEAL_COMMISSION);
                
                string deal_comment = HistoryDealGetString(deal_ticket, DEAL_COMMENT);
                datetime deal_time = (datetime)HistoryDealGetInteger(deal_ticket, DEAL_TIME);
                
                Print("交易记录 #", deal_ticket, " 时间:", TimeToString(deal_time), 
                      " 盈亏:", DoubleToString(deal_profit, 2), " 注释:", deal_comment);
                
                // 总体统计
                trade_stats.total_trades++;
                trade_stats.net_profit += deal_profit;
                
                if(deal_profit > 0) {
                    trade_stats.winning_trades++;
                } else if(deal_profit < 0) {
                    trade_stats.losing_trades++;
                }
                
                // 按策略分类统计
                if(StringFind(deal_comment, "突破") >= 0) {
                    trade_stats.breakout_total_trades++;
                    trade_stats.breakout_net_profit += deal_profit;
                    
                    if(deal_profit > 0) {
                        trade_stats.breakout_winning_trades++;
                    } else if(deal_profit < 0) {
                        trade_stats.breakout_losing_trades++;
                    }
                }
                else if(StringFind(deal_comment, "反转") >= 0) {
                    trade_stats.reversal_total_trades++;
                    trade_stats.reversal_net_profit += deal_profit;
                    
                    if(deal_profit > 0) {
                        trade_stats.reversal_winning_trades++;
                    } else if(deal_profit < 0) {
                        trade_stats.reversal_losing_trades++;
                    }
                }
            }
        }
    }
    
    // 计算胜率
    if(trade_stats.total_trades > 0) {
        trade_stats.win_rate = (double)trade_stats.winning_trades / trade_stats.total_trades * 100.0;
    }
    
    // 输出统计结果
    Print("=== 总体交易统计 ===");
    Print("总成交次数: ", trade_stats.total_trades);
    Print("盈利次数: ", trade_stats.winning_trades);
    Print("亏损次数: ", trade_stats.losing_trades);
    Print("胜率: ", DoubleToString(trade_stats.win_rate, 2), "%");
    Print("净利润: $", DoubleToString(trade_stats.net_profit, 2));
    
    Print("=== 突破策略统计 ===");
    Print("成交次数: ", trade_stats.breakout_total_trades);
    Print("盈利次数: ", trade_stats.breakout_winning_trades);
    Print("亏损次数: ", trade_stats.breakout_losing_trades);
    double breakout_win_rate = trade_stats.breakout_total_trades > 0 ? 
                              (double)trade_stats.breakout_winning_trades / trade_stats.breakout_total_trades * 100.0 : 0;
    Print("胜率: ", DoubleToString(breakout_win_rate, 2), "%");
    Print("净利润: $", DoubleToString(trade_stats.breakout_net_profit, 2));
    
    Print("=== 反转策略统计 ===");
    Print("成交次数: ", trade_stats.reversal_total_trades);
    Print("盈利次数: ", trade_stats.reversal_winning_trades);
    Print("亏损次数: ", trade_stats.reversal_losing_trades);
    double reversal_win_rate = trade_stats.reversal_total_trades > 0 ? 
                              (double)trade_stats.reversal_winning_trades / trade_stats.reversal_total_trades * 100.0 : 0;
    Print("胜率: ", DoubleToString(reversal_win_rate, 2), "%");
    Print("净利润: $", DoubleToString(trade_stats.reversal_net_profit, 2));
}
