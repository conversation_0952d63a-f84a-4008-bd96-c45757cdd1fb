//+------------------------------------------------------------------+
//|                                    TrendBreakoutReversal_EA.mq5 |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"

#include <Trade\Trade.mqh>

// 交易对象
CTrade trade;

// ================================
// 输入参数
// ================================

//--- 指标参数 ---
input group "========== 指标参数 =========="
input int    EMA20_Period = 20;           // EMA20周期
input int    EMA50_Period = 50;           // EMA50周期
input int    RSI_Period = 14;             // RSI周期
input int    Volume_MA_Period = 20;       // 成交量均线周期
input int    ATR_Period = 14;             // ATR周期

//--- 风险管理参数 ---
input group "========== 风险管理 =========="
input double Risk_Percent = 2.0;          // 风险比例(%)
input double Profit_Ratio = 2.0;          // 盈亏比
input bool   Enable_Trailing_Stop = true; // 启用追踪止损
input double Trailing_Start_Points = 200; // 追踪止损启动点数
input double Trailing_Stop_Points = 100;  // 追踪止损距离点数
input double Trailing_Step_Points = 50;   // 追踪止损步长点数

//--- 交易过滤参数 ---
input group "========== 交易过滤 =========="
input bool   Enable_MA_Filter = true;     // 启用均线过滤
input ENUM_TIMEFRAMES MA_Filter_Timeframe = PERIOD_H1; // 均线过滤时间周期
input int    MA_Filter_Period = 20;       // 均线过滤周期
input ENUM_MA_METHOD MA_Filter_Method = MODE_EMA; // 均线计算方法

//--- 时间过滤参数 ---
input group "========== 时间过滤 =========="
input bool   Enable_Time_Filter = false;  // 启用时间过滤
input bool   Trade_Monday = true;         // 周一交易
input bool   Trade_Tuesday = true;        // 周二交易
input bool   Trade_Wednesday = true;      // 周三交易
input bool   Trade_Thursday = true;       // 周四交易
input bool   Trade_Friday = true;         // 周五交易
input bool   Trade_Saturday = false;      // 周六交易
input bool   Trade_Sunday = false;        // 周日交易
input string Start_Time = "00:00";        // 开始交易时间
input string End_Time = "23:59";          // 结束交易时间

//--- 仓位管理参数 ---
input group "========== 仓位管理 =========="
input int    Max_Long_Positions = 3;      // 最大多头持仓数
input int    Magic_Number = 123456;       // 魔术数字

//--- 系统参数 ---
input group "========== 系统设置 =========="
input bool   Debug_Mode = true;           // 调试模式

// ================================
// 全局变量
// ================================
// 指标句柄
int ema20_handle, ema50_handle, rsi_handle, macd_handle, atr_handle;
int ma_filter_handle;  // 均线过滤句柄

// 指标数组
double ema20[], ema50[], rsi_values[], macd_main[], macd_signal[], atr_values[];
double ma_filter_values[];  // 均线过滤数组

// 追踪止损管理 - 使用并行数组（只支持多头）
ulong trailing_tickets[];
double trailing_entry_prices[];
double trailing_current_stops[];
bool trailing_actives[];

// 时间过滤变量
int start_time_seconds, end_time_seconds;

// 仪表盘监控变量
struct BreakoutMonitor {
    bool uptrend;
    bool volume_above_ma;
    bool price_above_highest;
    bool breakout_signal;
    double highest_high;
    double current_volume_ratio;
} breakout_monitor;

struct ReversalMonitor {
    bool oversold;
    bool hammer_pattern;
    bool rsi_divergence;
    bool price_near_ema50;
    bool reversal_signal;
    double current_rsi;
    double ema50_distance;
} reversal_monitor;

struct PositionInfo {
    double total_lots;
    int total_positions;
    double floating_profit;
    double next_risk_amount;
    double account_balance;
    double account_equity;
} position_info;

// 交易统计结构
struct TradeStatistics {
    // 总体统计
    int total_trades;
    int winning_trades;
    int losing_trades;
    double win_rate;
    double net_profit;

    // 突破策略统计
    int breakout_total_trades;
    int breakout_winning_trades;
    int breakout_losing_trades;
    double breakout_net_profit;

    // 反转策略统计
    int reversal_total_trades;
    int reversal_winning_trades;
    int reversal_losing_trades;
    double reversal_net_profit;
} trade_stats;

// 仪表盘状态缓存，避免不必要的更新
struct DashboardCache {
    bool uptrend_last;
    bool breakout_signal_last;
    bool reversal_signal_last;
    double highest_high_last;
    double current_rsi_last;
    double volume_ratio_last;
    int total_positions_last;
    double total_lots_last;
    double floating_profit_last;
    double next_risk_amount_last;
    bool initialized;
} dashboard_cache;

// 记录上次交易信号时间，避免重复开单
datetime last_breakout_signal_time = 0;
datetime last_reversal_signal_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 设置交易品种为XAUUSD
    if(Symbol() != "XAUUSD") {
        Print("警告：此EA专为XAUUSD设计，当前品种：", Symbol());
    }
    
    // 检查时间框架
    if(Period() != PERIOD_M3) {
        Print("警告：此EA设计用于3分钟图表，当前周期：", EnumToString(Period()));
    }
    
    // 初始化指标
    ema20_handle = iMA(Symbol(), PERIOD_M3, EMA20_Period, 0, MODE_EMA, PRICE_CLOSE);
    ema50_handle = iMA(Symbol(), PERIOD_M3, EMA50_Period, 0, MODE_EMA, PRICE_CLOSE);
    rsi_handle = iRSI(Symbol(), PERIOD_M3, RSI_Period, PRICE_CLOSE);
    macd_handle = iMACD(Symbol(), PERIOD_M3, 12, 26, 9, PRICE_CLOSE);
    atr_handle = iATR(Symbol(), PERIOD_M3, ATR_Period);

    // 初始化均线过滤指标
    if(Enable_MA_Filter) {
        ma_filter_handle = iMA(Symbol(), MA_Filter_Timeframe, MA_Filter_Period, 0, MA_Filter_Method, PRICE_CLOSE);
        if(ma_filter_handle == INVALID_HANDLE) {
            Print("均线过滤指标初始化失败");
            return INIT_FAILED;
        }
        ArraySetAsSeries(ma_filter_values, true);
    }

    // 检查指标句柄
    if(ema20_handle == INVALID_HANDLE || ema50_handle == INVALID_HANDLE ||
       rsi_handle == INVALID_HANDLE || macd_handle == INVALID_HANDLE ||
       atr_handle == INVALID_HANDLE) {
        Print("指标初始化失败");
        return INIT_FAILED;
    }

    // 设置数组为时间序列
    ArraySetAsSeries(ema20, true);
    ArraySetAsSeries(ema50, true);
    ArraySetAsSeries(rsi_values, true);
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);
    ArraySetAsSeries(atr_values, true);

    // 初始化时间过滤
    if(Enable_Time_Filter) {
        InitializeTimeFilter();
    }

    // 初始化追踪止损数组
    ArrayResize(trailing_tickets, 0);
    ArrayResize(trailing_entry_prices, 0);
    ArrayResize(trailing_current_stops, 0);
    ArrayResize(trailing_actives, 0);

    // 初始化交易统计
    InitializeTradeStatistics();

    // 设置魔术数字
    trade.SetExpertMagicNumber(Magic_Number);

    Print("趋势突破与反转确认做多策略 初始化成功");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 释放指标句柄
    IndicatorRelease(ema20_handle);
    IndicatorRelease(ema50_handle);
    IndicatorRelease(rsi_handle);
    IndicatorRelease(macd_handle);
    IndicatorRelease(atr_handle);

    // 释放均线过滤指标句柄
    if(Enable_MA_Filter && ma_filter_handle != INVALID_HANDLE) {
        IndicatorRelease(ma_filter_handle);
    }

    // 清除图表对象
    ObjectsDeleteAll(0, "Dashboard");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 获取指标数据
    if(!GetIndicatorData()) return;

    // 计算交易信号
    CalculateSignals();

    // 检查时间过滤
    if(Enable_Time_Filter && !IsTimeAllowed()) {
        UpdateDashboard();
        return;
    }

    // 管理追踪止损
    if(Enable_Trailing_Stop) {
        ManageTrailingStops();
    }

    // 检查持仓数量
    int long_positions = CountLongPositions();

    // 检查是否可以开新仓
    bool can_open_long = (long_positions < Max_Long_Positions);

    // 执行交易逻辑
    if(can_open_long) {
        ExecuteTrading(long_positions);
    }

    // 更新交易统计
    UpdateTradeStatistics();

    // 更新仪表盘
    UpdateDashboard();
}

//+------------------------------------------------------------------+
//| 获取指标数据                                                      |
//+------------------------------------------------------------------+
bool GetIndicatorData()
{
    // 复制指标数据
    if(CopyBuffer(ema20_handle, 0, 0, 25, ema20) <= 0) return false;
    if(CopyBuffer(ema50_handle, 0, 0, 25, ema50) <= 0) return false;
    if(CopyBuffer(rsi_handle, 0, 0, 25, rsi_values) <= 0) return false;
    if(CopyBuffer(macd_handle, 0, 0, 25, macd_main) <= 0) return false;
    if(CopyBuffer(macd_handle, 1, 0, 25, macd_signal) <= 0) return false;
    if(CopyBuffer(atr_handle, 0, 0, 25, atr_values) <= 0) return false;

    // 复制均线过滤数据
    if(Enable_MA_Filter) {
        if(CopyBuffer(ma_filter_handle, 0, 0, 5, ma_filter_values) <= 0) return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 计算交易信号                                                      |
//+------------------------------------------------------------------+
void CalculateSignals()
{
    // 获取价格数据
    double high[], low[], close[], open[];
    long volume[];

    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(open, true);
    ArraySetAsSeries(volume, true);

    CopyHigh(Symbol(), PERIOD_M3, 0, 25, high);
    CopyLow(Symbol(), PERIOD_M3, 0, 25, low);
    CopyClose(Symbol(), PERIOD_M3, 0, 25, close);
    CopyOpen(Symbol(), PERIOD_M3, 0, 25, open);
    CopyTickVolume(Symbol(), PERIOD_M3, 0, 25, volume);

    // ================================
    // 趋势判断
    // ================================
    double ema20_slope = ema20[0] - ema20[1];
    double ema50_slope = ema50[0] - ema50[1];
    breakout_monitor.uptrend = (ema20[0] > ema50[0] && ema20_slope > 0 && ema50_slope > 0);

    // ================================
    // 突破信号计算（修正）
    // ================================
    // 计算前20根K线的最高点（不包括当前K线）
    breakout_monitor.highest_high = high[1];
    for(int i = 2; i <= 20; i++) {
        if(high[i] > breakout_monitor.highest_high) {
            breakout_monitor.highest_high = high[i];
        }
    }

    // 成交量均线（修正：计算最近20根K线的平均，但不包括当前K线）
    double vol_ma = 0;
    for(int i = 1; i <= Volume_MA_Period; i++) {
        vol_ma += (double)volume[i];
    }
    vol_ma = vol_ma / Volume_MA_Period;

    // 突破条件
    breakout_monitor.price_above_highest = (close[0] > breakout_monitor.highest_high);
    breakout_monitor.volume_above_ma = ((double)volume[0] > vol_ma);
    breakout_monitor.current_volume_ratio = vol_ma > 0 ? (double)volume[0] / vol_ma : 0;

    bool breakout = breakout_monitor.price_above_highest && breakout_monitor.volume_above_ma;
    breakout_monitor.breakout_signal = breakout_monitor.uptrend && breakout;

    // ================================
    // 反转信号计算
    // ================================
    // RSI超卖
    reversal_monitor.current_rsi = rsi_values[0];
    reversal_monitor.oversold = (rsi_values[0] < 30);

    // 锤子线判断
    double body_size = MathAbs(open[0] - close[0]);
    double total_range = high[0] - low[0];
    double lower_shadow = MathMin(open[0], close[0]) - low[0];
    double upper_shadow = high[0] - MathMax(open[0], close[0]);

    // 避免除零错误
    if(total_range > 0) {
        reversal_monitor.hammer_pattern = (total_range > 3 * body_size) &&
                                         (lower_shadow > 0.6 * total_range) &&
                                         (upper_shadow < 0.3 * total_range);
    } else {
        reversal_monitor.hammer_pattern = false;
    }

    // RSI底背离
    double lowest_price = low[1];
    double lowest_rsi = rsi_values[1];
    for(int i = 2; i <= 5; i++) {
        if(low[i] < lowest_price) lowest_price = low[i];
        if(rsi_values[i] < lowest_rsi) lowest_rsi = rsi_values[i];
    }

    bool price_making_lower = (low[0] < lowest_price);
    bool rsi_not_making_lower = (rsi_values[0] > lowest_rsi);
    reversal_monitor.rsi_divergence = price_making_lower && rsi_not_making_lower;

    // 均线支撑
    reversal_monitor.ema50_distance = MathAbs(close[0] - ema50[0]);
    reversal_monitor.price_near_ema50 = (reversal_monitor.ema50_distance < atr_values[0]);

    // 反转确认
    bool reversal = reversal_monitor.oversold &&
                   ((reversal_monitor.hammer_pattern && reversal_monitor.rsi_divergence) ||
                    (reversal_monitor.hammer_pattern && reversal_monitor.price_near_ema50) ||
                    (reversal_monitor.rsi_divergence && reversal_monitor.price_near_ema50));

    reversal_monitor.reversal_signal = !breakout_monitor.uptrend && reversal;
    
    // 调试信息
    if(Debug_Mode) {
        static datetime last_debug_time = 0;
        datetime current_time = TimeCurrent();
        if(current_time - last_debug_time >= 60) {  // 每分钟输出一次
            Print("===== 信号状态 =====");
            Print("当前价格: ", close[0], " 前高: ", breakout_monitor.highest_high);
            Print("EMA20: ", ema20[0], " EMA50: ", ema50[0]);
            Print("突破信号: ", breakout_monitor.breakout_signal, 
                  " (趋势:", breakout_monitor.uptrend, 
                  " 价格突破:", breakout_monitor.price_above_highest,
                  " 成交量:", breakout_monitor.volume_above_ma, ")");
            Print("反转信号: ", reversal_monitor.reversal_signal,
                  " (超卖:", reversal_monitor.oversold,
                  " RSI:", reversal_monitor.current_rsi,
                  " 锤子线:", reversal_monitor.hammer_pattern,
                  " 背离:", reversal_monitor.rsi_divergence,
                  " 近EMA50:", reversal_monitor.price_near_ema50, ")");
            last_debug_time = current_time;
        }
    }
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                      |
//+------------------------------------------------------------------+
double GetPositionSize(double stop_price, int current_positions)
{
    double risk_amount = AccountInfoDouble(ACCOUNT_EQUITY) * Risk_Percent / 100.0;
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double stop_distance = MathAbs(current_price - stop_price);

    if(stop_distance <= 0) {
        Print("错误：止损距离为0");
        return 0;
    }

    double tick_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

    double value_per_point = tick_value / tick_size;
    double base_position_size = risk_amount / (stop_distance * value_per_point);

    // 根据持仓数量调整手数：第2单减半，第3单再减半，以此类推
    double position_size = base_position_size;
    for(int i = 0; i < current_positions; i++) {
        position_size = position_size / 2.0;
    }

    // 标准化手数
    double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

    position_size = MathMax(min_lot, MathMin(max_lot,
                   MathFloor(position_size / lot_step) * lot_step));

    if(Debug_Mode) {
        Print("仓位计算 - 当前持仓数:", current_positions,
              " 基础手数:", base_position_size,
              " 调整后手数:", position_size,
              " 风险金额:", risk_amount,
              " 止损距离:", stop_distance);
    }

    return position_size;
}

//+------------------------------------------------------------------+
//| 执行交易逻辑                                                      |
//+------------------------------------------------------------------+
void ExecuteTrading(int current_positions)
{
    datetime current_time = TimeCurrent();
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);

    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);

    CopyHigh(Symbol(), PERIOD_M3, 0, 10, high);
    CopyLow(Symbol(), PERIOD_M3, 0, 10, low);
    CopyClose(Symbol(), PERIOD_M3, 0, 10, close);

    // ================================
    // 突破做多信号
    // ================================
    if(breakout_monitor.breakout_signal) {
        // 检查均线过滤条件
        bool ma_filter_ok = true;
        if(Enable_MA_Filter) {
            ma_filter_ok = (current_price > ma_filter_values[0]);
            if(Debug_Mode) {
                Print("均线过滤检查 - 当前价格:", current_price,
                      " 过滤均线:", ma_filter_values[0],
                      " 结果:", ma_filter_ok ? "通过" : "不通过");
            }
        }

        if(ma_filter_ok) {
        // 检查是否是新信号（避免重复开单）
        if(current_time - last_breakout_signal_time > 180) { // 3分钟冷却期
            // 计算止损价格（前5根K线最低点）
            double breakout_stop = low[1];
            for(int i = 2; i <= 5; i++) {
                if(low[i] < breakout_stop) breakout_stop = low[i];
            }

            double breakout_pos_size = GetPositionSize(breakout_stop, current_positions);

            if(breakout_pos_size > 0) {
                // 计算止盈价格
                double stop_distance = current_price - breakout_stop;
                double take_profit = current_price + stop_distance * Profit_Ratio;

                // 开仓（如果启用追踪止损，则不设置固定止盈）
                double final_take_profit = Enable_Trailing_Stop ? 0 : take_profit;

                if(trade.Buy(breakout_pos_size, Symbol(), current_price, breakout_stop, final_take_profit, "突破做多")) {
                    Print("突破做多开仓成功 - 第", (current_positions + 1), "单",
                          " 手数:", breakout_pos_size,
                          " 当前持仓数:", current_positions,
                          " 入场价:", current_price,
                          " 止损:", breakout_stop,
                          " 止盈:", final_take_profit > 0 ? DoubleToString(final_take_profit) : "追踪止损");
                    last_breakout_signal_time = current_time;
                } else {
                    Print("突破做多开仓失败 - 错误:", GetLastError());
                }
            }
        }
        }
    }

    // ================================
    // 反转做多信号
    // ================================
    if(reversal_monitor.reversal_signal) {
        // 检查是否是新信号（避免重复开单）
        if(current_time - last_reversal_signal_time > 180) { // 3分钟冷却期
            // 计算止损价格
            double reversal_stop = low[0] - atr_values[0] * 0.5;
            double reversal_pos_size = GetPositionSize(reversal_stop, current_positions);

            if(Debug_Mode) {
                Print("反转信号触发 - 当前最低价:", low[0],
                      " 止损价:", reversal_stop,
                      " ATR:", atr_values[0]);
            }

            if(reversal_pos_size > 0) {
                // 计算止盈价格
                double stop_distance = current_price - reversal_stop;
                double take_profit = current_price + stop_distance * Profit_Ratio;

                // 开仓（如果启用追踪止损，则不设置固定止盈）
                double final_take_profit = Enable_Trailing_Stop ? 0 : take_profit;

                if(trade.Buy(reversal_pos_size, Symbol(), current_price, reversal_stop, final_take_profit, "反转做多")) {
                    Print("反转做多开仓成功 - 第", (current_positions + 1), "单",
                          " 手数:", reversal_pos_size,
                          " 当前持仓数:", current_positions,
                          " 入场价:", current_price,
                          " 止损:", reversal_stop,
                          " 止盈:", final_take_profit > 0 ? DoubleToString(final_take_profit) : "追踪止损");
                    last_reversal_signal_time = current_time;
                } else {
                    Print("反转做多开仓失败 - 错误:", GetLastError());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 更新仓位信息                                                      |
//+------------------------------------------------------------------+
void UpdatePositionInfo()
{
    position_info.total_lots = 0;
    position_info.total_positions = 0;
    position_info.floating_profit = 0;

    // 统计当前仓位
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByTicket(PositionGetTicket(i))) {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == Magic_Number) {
                position_info.total_lots += PositionGetDouble(POSITION_VOLUME);
                position_info.total_positions++;
                position_info.floating_profit += PositionGetDouble(POSITION_PROFIT);
            }
        }
    }

    // 账户信息
    position_info.account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    position_info.account_equity = AccountInfoDouble(ACCOUNT_EQUITY);

    // 计算下一单风险金额
    position_info.next_risk_amount = position_info.account_equity * Risk_Percent / 100.0;
}

//+------------------------------------------------------------------+
//| 更新仪表盘                                                        |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
    UpdatePositionInfo();

    // 检查是否需要更新仪表盘
    if(!NeedsDashboardUpdate()) {
        return; // 如果没有变化，直接返回
    }

    // 使用更温和的更新方式，避免闪烁
    static datetime last_full_update = 0;
    datetime current_time = TimeCurrent();

    // 每5秒才允许一次完全重建，其他时候只更新文本
    bool force_rebuild = (current_time - last_full_update > 5) || !dashboard_cache.initialized;

    if(force_rebuild) {
        // 清除旧的仪表盘对象
        ObjectsDeleteAll(0, "Dashboard");
        last_full_update = current_time;
    }

    int x_start = 20;
    int y_start = 50;
    int column_width = 280;
    int row_height = 25;  // 增加行间距，避免重叠
    int dashboard_width = column_width * 3 + 40;  // 总宽度
    int dashboard_height = row_height * 18 + 20;   // 增加高度以容纳交易统计

    // 只在完全重建时创建背景和分隔符
    if(force_rebuild) {
        // ================================
        // 创建背景
        // ================================
        CreateBackground("Dashboard_Background", x_start - 10, y_start - 10,
                        dashboard_width, dashboard_height);

        // ================================
        // 创建竖线分隔符
        // ================================
        int separator1_x = x_start + column_width - 10;
        int separator2_x = x_start + column_width * 2 - 10;

        CreateVerticalLine("Dashboard_Separator1", separator1_x, y_start - 5, dashboard_height - 10);
        CreateVerticalLine("Dashboard_Separator2", separator2_x, y_start - 5, dashboard_height - 10);
    }

    // ================================
    // 第一列：突破策略监控
    // ================================
    UpdateLabel("Dashboard_Title1", x_start, y_start, "=== 突破策略监控 ===", clrYellow, 12);

    UpdateLabel("Dashboard_Uptrend", x_start, y_start + row_height * 2,
                "趋势状态: " + (breakout_monitor.uptrend ? "上升趋势 ✓" : "非上升趋势 ✗"),
                breakout_monitor.uptrend ? clrLime : clrRed);

    UpdateLabel("Dashboard_HighestHigh", x_start, y_start + row_height * 3,
                "前高价格: " + DoubleToString(breakout_monitor.highest_high, _Digits), clrWhite);

    UpdateLabel("Dashboard_PriceBreak", x_start, y_start + row_height * 4,
                "价格突破: " + (breakout_monitor.price_above_highest ? "已突破 ✓" : "未突破 ✗"),
                breakout_monitor.price_above_highest ? clrLime : clrRed);

    UpdateLabel("Dashboard_VolumeRatio", x_start, y_start + row_height * 5,
                "成交量比: " + DoubleToString(breakout_monitor.current_volume_ratio, 2),
                breakout_monitor.volume_above_ma ? clrLime : clrRed);

    UpdateLabel("Dashboard_BreakoutSignal", x_start, y_start + row_height * 6,
                "突破信号: " + (breakout_monitor.breakout_signal ? "触发 ✓" : "未触发 ✗"),
                breakout_monitor.breakout_signal ? clrYellow : clrGray);

    // ================================
    // 第二列：反转策略监控
    // ================================
    int x_col2 = x_start + column_width;

    UpdateLabel("Dashboard_Title2", x_col2, y_start, "=== 反转策略监控 ===", clrYellow, 12);

    UpdateLabel("Dashboard_RSI", x_col2, y_start + row_height * 2,
                "RSI值: " + DoubleToString(reversal_monitor.current_rsi, 2),
                reversal_monitor.oversold ? clrLime : clrWhite);

    UpdateLabel("Dashboard_Oversold", x_col2, y_start + row_height * 3,
                "RSI超卖: " + (reversal_monitor.oversold ? "是 ✓" : "否 ✗"),
                reversal_monitor.oversold ? clrLime : clrRed);

    UpdateLabel("Dashboard_Hammer", x_col2, y_start + row_height * 4,
                "锤子线: " + (reversal_monitor.hammer_pattern ? "形成 ✓" : "未形成 ✗"),
                reversal_monitor.hammer_pattern ? clrLime : clrRed);

    UpdateLabel("Dashboard_Divergence", x_col2, y_start + row_height * 5,
                "RSI背离: " + (reversal_monitor.rsi_divergence ? "存在 ✓" : "不存在 ✗"),
                reversal_monitor.rsi_divergence ? clrLime : clrRed);

    UpdateLabel("Dashboard_EMA50Dist", x_col2, y_start + row_height * 6,
                "EMA50距离: " + DoubleToString(reversal_monitor.ema50_distance, _Digits),
                reversal_monitor.price_near_ema50 ? clrLime : clrWhite);

    UpdateLabel("Dashboard_ReversalSignal", x_col2, y_start + row_height * 7,
                "反转信号: " + (reversal_monitor.reversal_signal ? "触发 ✓" : "未触发 ✗"),
                reversal_monitor.reversal_signal ? clrYellow : clrGray);

    // ================================
    // 第三列：仓位信息
    // ================================
    int x_col3 = x_start + column_width * 2;

    UpdateLabel("Dashboard_Title3", x_col3, y_start, "=== 仓位信息 ===", clrYellow, 12);

    UpdateLabel("Dashboard_TotalPos", x_col3, y_start + row_height * 2,
                "总仓位数: " + IntegerToString(position_info.total_positions), clrWhite);

    UpdateLabel("Dashboard_TotalLots", x_col3, y_start + row_height * 3,
                "总手数: " + DoubleToString(position_info.total_lots, 2), clrWhite);

    UpdateLabel("Dashboard_FloatingPL", x_col3, y_start + row_height * 4,
                "浮动盈亏: $" + DoubleToString(position_info.floating_profit, 2),
                position_info.floating_profit >= 0 ? clrLime : clrRed);

    UpdateLabel("Dashboard_Balance", x_col3, y_start + row_height * 5,
                "账户余额: $" + DoubleToString(position_info.account_balance, 2), clrWhite);

    UpdateLabel("Dashboard_Equity", x_col3, y_start + row_height * 6,
                "账户净值: $" + DoubleToString(position_info.account_equity, 2), clrWhite);

    UpdateLabel("Dashboard_NextRisk", x_col3, y_start + row_height * 7,
                "下单风险: $" + DoubleToString(position_info.next_risk_amount, 2), clrYellow);

    UpdateLabel("Dashboard_RiskPercent", x_col3, y_start + row_height * 8,
                "风险比例: " + DoubleToString(Risk_Percent, 1) + "%", clrWhite);

    // ================================
    // 交易统计区域（横跨三列）
    // ================================
    int stats_y_start = y_start + row_height * 10;

    // 总体统计标题
    UpdateLabel("Dashboard_StatsTitle", x_start, stats_y_start, "=== 交易统计 ===", clrYellow, 12);

    // 总体统计
    UpdateLabel("Dashboard_TotalTrades", x_start, stats_y_start + row_height * 1,
                "总成交次数: " + IntegerToString(trade_stats.total_trades), clrWhite);

    UpdateLabel("Dashboard_WinTrades", x_start, stats_y_start + row_height * 2,
                "盈利次数: " + IntegerToString(trade_stats.winning_trades), clrLime);

    UpdateLabel("Dashboard_LossTrades", x_start, stats_y_start + row_height * 3,
                "亏损次数: " + IntegerToString(trade_stats.losing_trades), clrRed);

    UpdateLabel("Dashboard_WinRate", x_start, stats_y_start + row_height * 4,
                "胜率: " + DoubleToString(trade_stats.win_rate, 2) + "%",
                trade_stats.win_rate >= 50 ? clrLime : clrRed);

    UpdateLabel("Dashboard_NetProfit", x_start, stats_y_start + row_height * 5,
                "净利润: $" + DoubleToString(trade_stats.net_profit, 2),
                trade_stats.net_profit >= 0 ? clrLime : clrRed);

    // 突破策略统计
    UpdateLabel("Dashboard_BreakoutStatsTitle", x_col2, stats_y_start, "=== 突破策略统计 ===", clrYellow, 12);

    UpdateLabel("Dashboard_BreakoutTotalTrades", x_col2, stats_y_start + row_height * 1,
                "成交次数: " + IntegerToString(trade_stats.breakout_total_trades), clrWhite);

    UpdateLabel("Dashboard_BreakoutWinTrades", x_col2, stats_y_start + row_height * 2,
                "盈利次数: " + IntegerToString(trade_stats.breakout_winning_trades), clrLime);

    UpdateLabel("Dashboard_BreakoutLossTrades", x_col2, stats_y_start + row_height * 3,
                "亏损次数: " + IntegerToString(trade_stats.breakout_losing_trades), clrRed);

    double breakout_win_rate = trade_stats.breakout_total_trades > 0 ?
                              (double)trade_stats.breakout_winning_trades / trade_stats.breakout_total_trades * 100.0 : 0;
    UpdateLabel("Dashboard_BreakoutWinRate", x_col2, stats_y_start + row_height * 4,
                "胜率: " + DoubleToString(breakout_win_rate, 2) + "%",
                breakout_win_rate >= 50 ? clrLime : clrRed);

    UpdateLabel("Dashboard_BreakoutNetProfit", x_col2, stats_y_start + row_height * 5,
                "净利润: $" + DoubleToString(trade_stats.breakout_net_profit, 2),
                trade_stats.breakout_net_profit >= 0 ? clrLime : clrRed);

    // 反转策略统计
    UpdateLabel("Dashboard_ReversalStatsTitle", x_col3, stats_y_start, "=== 反转策略统计 ===", clrYellow, 12);

    UpdateLabel("Dashboard_ReversalTotalTrades", x_col3, stats_y_start + row_height * 1,
                "成交次数: " + IntegerToString(trade_stats.reversal_total_trades), clrWhite);

    UpdateLabel("Dashboard_ReversalWinTrades", x_col3, stats_y_start + row_height * 2,
                "盈利次数: " + IntegerToString(trade_stats.reversal_winning_trades), clrLime);

    UpdateLabel("Dashboard_ReversalLossTrades", x_col3, stats_y_start + row_height * 3,
                "亏损次数: " + IntegerToString(trade_stats.reversal_losing_trades), clrRed);

    double reversal_win_rate = trade_stats.reversal_total_trades > 0 ?
                              (double)trade_stats.reversal_winning_trades / trade_stats.reversal_total_trades * 100.0 : 0;
    UpdateLabel("Dashboard_ReversalWinRate", x_col3, stats_y_start + row_height * 4,
                "胜率: " + DoubleToString(reversal_win_rate, 2) + "%",
                reversal_win_rate >= 50 ? clrLime : clrRed);

    UpdateLabel("Dashboard_ReversalNetProfit", x_col3, stats_y_start + row_height * 5,
                "净利润: $" + DoubleToString(trade_stats.reversal_net_profit, 2),
                trade_stats.reversal_net_profit >= 0 ? clrLime : clrRed);

    // 更新缓存
    UpdateDashboardCache();
}

//+------------------------------------------------------------------+
//| 检查是否需要更新仪表盘                                             |
//+------------------------------------------------------------------+
bool NeedsDashboardUpdate()
{
    // 首次初始化
    if(!dashboard_cache.initialized) {
        return true;
    }

    // 使用更严格的阈值减少闪烁，只在重要变化时更新
    if(dashboard_cache.uptrend_last != breakout_monitor.uptrend ||
       dashboard_cache.breakout_signal_last != breakout_monitor.breakout_signal ||
       dashboard_cache.reversal_signal_last != reversal_monitor.reversal_signal ||
       MathAbs(dashboard_cache.highest_high_last - breakout_monitor.highest_high) > _Point * 10 ||
       MathAbs(dashboard_cache.current_rsi_last - reversal_monitor.current_rsi) > 1.0 ||
       MathAbs(dashboard_cache.volume_ratio_last - breakout_monitor.current_volume_ratio) > 0.1 ||
       dashboard_cache.total_positions_last != position_info.total_positions ||
       MathAbs(dashboard_cache.total_lots_last - position_info.total_lots) > 0.01 ||
       MathAbs(dashboard_cache.floating_profit_last - position_info.floating_profit) > 5.0 ||
       MathAbs(dashboard_cache.next_risk_amount_last - position_info.next_risk_amount) > 5.0) {
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| 更新仪表盘缓存                                                    |
//+------------------------------------------------------------------+
void UpdateDashboardCache()
{
    dashboard_cache.uptrend_last = breakout_monitor.uptrend;
    dashboard_cache.breakout_signal_last = breakout_monitor.breakout_signal;
    dashboard_cache.reversal_signal_last = reversal_monitor.reversal_signal;
    dashboard_cache.highest_high_last = breakout_monitor.highest_high;
    dashboard_cache.current_rsi_last = reversal_monitor.current_rsi;
    dashboard_cache.volume_ratio_last = breakout_monitor.current_volume_ratio;
    dashboard_cache.total_positions_last = position_info.total_positions;
    dashboard_cache.total_lots_last = position_info.total_lots;
    dashboard_cache.floating_profit_last = position_info.floating_profit;
    dashboard_cache.next_risk_amount_last = position_info.next_risk_amount;
    dashboard_cache.initialized = true;
}

//+------------------------------------------------------------------+
//| 创建背景矩形                                                      |
//+------------------------------------------------------------------+
void CreateBackground(string name, int x, int y, int width, int height)
{
    ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, C'40,40,40');  // 更深的不透明背景
    ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, name, OBJPROP_BORDER_COLOR, C'80,80,80');  // 更明显的边框
    ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);  // 边框宽度
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_BACK, true);  // 放在背景层
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, false);  // 确保可见
}

//+------------------------------------------------------------------+
//| 创建竖线分隔符                                                    |
//+------------------------------------------------------------------+
void CreateVerticalLine(string name, int x, int y, int height)
{
    ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, name, OBJPROP_XSIZE, 2);  // 线宽度为2像素
    ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, name, OBJPROP_BGCOLOR, clrGray);
    ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
}

//+------------------------------------------------------------------+
//| 创建文本标签                                                      |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, color clr, int font_size = 10)
{
    ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
}

//+------------------------------------------------------------------+
//| 智能更新标签（只更新文本，避免重建）                                |
//+------------------------------------------------------------------+
void UpdateLabel(string name, int x, int y, string text, color clr, int font_size = 10)
{
    // 如果对象不存在，创建它
    if(ObjectFind(0, name) < 0) {
        CreateLabel(name, x, y, text, clr, font_size);
    } else {
        // 如果对象存在，只更新文本和颜色
        ObjectSetString(0, name, OBJPROP_TEXT, text);
        ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    }
}

//+------------------------------------------------------------------+
//| 初始化时间过滤                                                    |
//+------------------------------------------------------------------+
void InitializeTimeFilter()
{
    // 解析开始时间
    string start_parts[];
    StringSplit(Start_Time, ':', start_parts);
    if(ArraySize(start_parts) >= 2) {
        int start_hour = (int)StringToInteger(start_parts[0]);
        int start_minute = (int)StringToInteger(start_parts[1]);
        start_time_seconds = start_hour * 3600 + start_minute * 60;
    }

    // 解析结束时间
    string end_parts[];
    StringSplit(End_Time, ':', end_parts);
    if(ArraySize(end_parts) >= 2) {
        int end_hour = (int)StringToInteger(end_parts[0]);
        int end_minute = (int)StringToInteger(end_parts[1]);
        end_time_seconds = end_hour * 3600 + end_minute * 60;
    }
}

//+------------------------------------------------------------------+
//| 检查时间是否允许交易                                               |
//+------------------------------------------------------------------+
bool IsTimeAllowed()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    // 检查星期
    bool day_allowed = false;
    switch(dt.day_of_week) {
        case 1: day_allowed = Trade_Monday; break;
        case 2: day_allowed = Trade_Tuesday; break;
        case 3: day_allowed = Trade_Wednesday; break;
        case 4: day_allowed = Trade_Thursday; break;
        case 5: day_allowed = Trade_Friday; break;
        case 6: day_allowed = Trade_Saturday; break;
        case 0: day_allowed = Trade_Sunday; break;
    }

    if(!day_allowed) return false;

    // 检查时间
    int current_seconds = dt.hour * 3600 + dt.min * 60;

    if(start_time_seconds <= end_time_seconds) {
        // 同一天内的时间范围
        return (current_seconds >= start_time_seconds && current_seconds <= end_time_seconds);
    } else {
        // 跨天的时间范围
        return (current_seconds >= start_time_seconds || current_seconds <= end_time_seconds);
    }
}

//+------------------------------------------------------------------+
//| 计算多头持仓数量                                                   |
//+------------------------------------------------------------------+
int CountLongPositions()
{
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByTicket(PositionGetTicket(i))) {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
               PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| 管理追踪止损                                                      |
//+------------------------------------------------------------------+
void ManageTrailingStops()
{
    double current_bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double current_ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

    // 更新现有持仓的追踪止损
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByTicket(PositionGetTicket(i))) {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == Magic_Number) {

                ulong ticket = PositionGetInteger(POSITION_TICKET);
                ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
                double current_stop = PositionGetDouble(POSITION_SL);

                // 查找或创建追踪止损记录
                int trailing_index = FindTrailingStopIndex(ticket);
                if(trailing_index == -1) {
                    // 创建新的追踪止损记录
                    trailing_index = ArraySize(trailing_tickets);
                    ArrayResize(trailing_tickets, trailing_index + 1);
                    ArrayResize(trailing_entry_prices, trailing_index + 1);
                    ArrayResize(trailing_current_stops, trailing_index + 1);
                    ArrayResize(trailing_actives, trailing_index + 1);

                    trailing_tickets[trailing_index] = ticket;
                    trailing_entry_prices[trailing_index] = entry_price;
                    trailing_current_stops[trailing_index] = current_stop;
                    trailing_actives[trailing_index] = false;
                }

                // 计算追踪止损
                UpdateTrailingStop(trailing_index, current_bid, current_ask, point);
            }
        }
    }

    // 清理已关闭持仓的追踪止损记录
    CleanupTrailingStops();
}

//+------------------------------------------------------------------+
//| 查找追踪止损记录索引                                               |
//+------------------------------------------------------------------+
int FindTrailingStopIndex(ulong ticket)
{
    for(int i = 0; i < ArraySize(trailing_tickets); i++) {
        if(trailing_tickets[i] == ticket) {
            return i;
        }
    }
    return -1;
}

//+------------------------------------------------------------------+
//| 更新追踪止损                                                      |
//+------------------------------------------------------------------+
void UpdateTrailingStop(int index, double current_bid, double current_ask, double point)
{
    // 多头持仓追踪止损
    double profit_points = (current_bid - trailing_entry_prices[index]) / point;

    if(!trailing_actives[index] && profit_points >= Trailing_Start_Points) {
        // 激活追踪止损
        trailing_actives[index] = true;
        trailing_current_stops[index] = current_bid - Trailing_Stop_Points * point;

        if(Debug_Mode) {
            Print("追踪止损激活 - 订单:", trailing_tickets[index], " 新止损:", trailing_current_stops[index]);
        }
    }

    if(trailing_actives[index]) {
        double new_stop = current_bid - Trailing_Stop_Points * point;
        if(new_stop > trailing_current_stops[index] + Trailing_Step_Points * point) {
            trailing_current_stops[index] = new_stop;

            // 修改止损
            if(!trade.PositionModify(trailing_tickets[index], trailing_current_stops[index], 0)) {
                if(Debug_Mode) {
                    Print("修改追踪止损失败 - 订单:", trailing_tickets[index], " 错误:", GetLastError());
                }
            } else {
                if(Debug_Mode) {
                    Print("追踪止损更新 - 订单:", trailing_tickets[index], " 新止损:", trailing_current_stops[index]);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 清理已关闭持仓的追踪止损记录                                       |
//+------------------------------------------------------------------+
void CleanupTrailingStops()
{
    for(int i = ArraySize(trailing_tickets) - 1; i >= 0; i--) {
        bool position_exists = false;

        // 检查持仓是否还存在
        for(int j = 0; j < PositionsTotal(); j++) {
            if(PositionSelectByTicket(PositionGetTicket(j))) {
                if(PositionGetInteger(POSITION_TICKET) == trailing_tickets[i]) {
                    position_exists = true;
                    break;
                }
            }
        }

        // 如果持仓不存在，删除追踪止损记录
        if(!position_exists) {
            // 移动数组元素
            for(int k = i; k < ArraySize(trailing_tickets) - 1; k++) {
                trailing_tickets[k] = trailing_tickets[k + 1];
                trailing_entry_prices[k] = trailing_entry_prices[k + 1];
                trailing_current_stops[k] = trailing_current_stops[k + 1];
                trailing_actives[k] = trailing_actives[k + 1];
            }

            // 调整数组大小
            int new_size = ArraySize(trailing_tickets) - 1;
            ArrayResize(trailing_tickets, new_size);
            ArrayResize(trailing_entry_prices, new_size);
            ArrayResize(trailing_current_stops, new_size);
            ArrayResize(trailing_actives, new_size);
        }
    }
}

//+------------------------------------------------------------------+
//| 初始化交易统计                                                    |
//+------------------------------------------------------------------+
void InitializeTradeStatistics()
{
    trade_stats.total_trades = 0;
    trade_stats.winning_trades = 0;
    trade_stats.losing_trades = 0;
    trade_stats.win_rate = 0;
    trade_stats.net_profit = 0;

    trade_stats.breakout_total_trades = 0;
    trade_stats.breakout_winning_trades = 0;
    trade_stats.breakout_losing_trades = 0;
    trade_stats.breakout_net_profit = 0;

    trade_stats.reversal_total_trades = 0;
    trade_stats.reversal_winning_trades = 0;
    trade_stats.reversal_losing_trades = 0;
    trade_stats.reversal_net_profit = 0;
}

//+------------------------------------------------------------------+
//| 更新交易统计                                                      |
//+------------------------------------------------------------------+
void UpdateTradeStatistics()
{
    // 重置统计数据
    InitializeTradeStatistics();

    // 获取历史交易数据需要先选择历史
    if(!HistorySelect(0, TimeCurrent())) {
        Print("无法获取历史交易数据");
        return;
    }

    // 统计历史交易
    for(int i = 0; i < HistoryDealsTotal(); i++) {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if(deal_ticket > 0) {
            if(HistoryDealGetString(deal_ticket, DEAL_SYMBOL) == Symbol() &&
               HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) == Magic_Number &&
               HistoryDealGetInteger(deal_ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT) {

                double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT) +
                                   HistoryDealGetDouble(deal_ticket, DEAL_SWAP) +
                                   HistoryDealGetDouble(deal_ticket, DEAL_COMMISSION);

                string deal_comment = HistoryDealGetString(deal_ticket, DEAL_COMMENT);

                // 总体统计
                trade_stats.total_trades++;
                trade_stats.net_profit += deal_profit;

                if(deal_profit > 0) {
                    trade_stats.winning_trades++;
                } else if(deal_profit < 0) {
                    trade_stats.losing_trades++;
                }

                // 按策略分类统计
                if(StringFind(deal_comment, "突破") >= 0) {
                    trade_stats.breakout_total_trades++;
                    trade_stats.breakout_net_profit += deal_profit;

                    if(deal_profit > 0) {
                        trade_stats.breakout_winning_trades++;
                    } else if(deal_profit < 0) {
                        trade_stats.breakout_losing_trades++;
                    }
                }
                else if(StringFind(deal_comment, "反转") >= 0) {
                    trade_stats.reversal_total_trades++;
                    trade_stats.reversal_net_profit += deal_profit;

                    if(deal_profit > 0) {
                        trade_stats.reversal_winning_trades++;
                    } else if(deal_profit < 0) {
                        trade_stats.reversal_losing_trades++;
                    }
                }
            }
        }
    }

    // 计算胜率
    if(trade_stats.total_trades > 0) {
        trade_stats.win_rate = (double)trade_stats.winning_trades / trade_stats.total_trades * 100.0;
    }
}
