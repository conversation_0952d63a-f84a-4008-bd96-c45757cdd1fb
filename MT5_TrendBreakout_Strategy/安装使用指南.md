# MT5 趋势突破与反转策略 - 安装使用指南

## 文件说明

本项目包含以下文件：

1. **TrendBreakoutReversal_EA.mq5** - 完整的交易EA，包含三列仪表盘
2. **TrendBreakoutReversal_Test.mq5** - 测试版指标，用于验证策略逻辑
3. **README.md** - 详细的策略说明文档
4. **安装使用指南.md** - 本文件

## 安装步骤

### 第一步：复制文件到MT5目录

1. 打开MT5平台
2. 按 `Ctrl + Shift + D` 打开数据文件夹
3. 将文件复制到对应目录：
   - `TrendBreakoutReversal_EA.mq5` → `MQL5/Experts/` 目录
   - `TrendBreakoutReversal_Test.mq5` → `MQL5/Indicators/` 目录

### 第二步：编译文件

1. 在MT5中按 `F4` 打开MetaEditor
2. 打开复制的文件
3. 按 `F7` 编译文件
4. 确保编译无错误

## 使用步骤

### 方法一：先使用测试版验证（推荐）

1. **打开XAUUSD图表**
   - 在市场报价中找到XAUUSD
   - 右键选择"图表窗口"

2. **设置3分钟时间框架**
   - 在图表上右键 → 时间周期 → M3

3. **添加测试指标**
   - 在导航器中找到"指标" → "自定义"
   - 双击 `TrendBreakoutReversal_Test`
   - 点击"确定"

4. **观察测试结果**
   - 查看左上角的监控面板
   - 验证各项指标是否正常显示
   - 观察信号触发情况

### 方法二：使用完整EA进行交易

⚠️ **重要提示：请先在模拟账户中测试！**

1. **准备工作**
   - 确保使用XAUUSD品种
   - 设置为M3（3分钟）时间框架
   - 建议先在模拟账户测试

2. **添加EA到图表**
   - 在导航器中找到"专家顾问"
   - 双击 `TrendBreakoutReversal_EA`

3. **配置参数**
   ```
   EMA20_Period = 20        // EMA20周期
   EMA50_Period = 50        // EMA50周期  
   RSI_Period = 14          // RSI周期
   Volume_MA_Period = 20    // 成交量均线周期
   Risk_Percent = 2.0       // 风险比例(%) - 建议从1%开始
   ATR_Period = 14          // ATR周期
   Profit_Ratio = 2.0       // 盈亏比
   Magic_Number = 123456    // 魔术数字
   ```

4. **启用自动交易**
   - 确保MT5工具栏上的"自动交易"按钮是绿色的
   - 如果是红色，点击启用

5. **监控仪表盘**
   - 左侧：突破策略监控
   - 中间：反转策略监控  
   - 右侧：仓位信息

## 仪表盘说明

### 突破策略监控（第一列）
- **趋势状态**：显示当前市场趋势方向
- **前高价格**：显示需要突破的价格水平
- **价格突破**：显示是否已突破关键价位
- **成交量比**：显示成交量确认情况
- **突破信号**：显示是否满足所有突破条件

### 反转策略监控（第二列）
- **RSI值**：显示当前RSI指标数值
- **RSI超卖**：显示是否处于超卖状态
- **锤子线**：显示是否形成反转K线形态
- **RSI背离**：显示是否存在价格与RSI背离
- **EMA50距离**：显示价格与均线支撑的距离
- **反转信号**：显示是否满足所有反转条件

### 仓位信息（第三列）
- **总仓位数**：当前持有的交易数量
- **总手数**：当前持有的总交易量
- **浮动盈亏**：当前未平仓交易的盈亏
- **账户余额**：账户资金余额
- **账户净值**：包含浮动盈亏的净资产
- **下单风险**：下一笔交易的风险金额
- **风险比例**：设定的风险管理比例

## 重要注意事项

### 风险管理
1. **从小资金开始**：建议风险比例从1%开始
2. **模拟测试**：实盘前务必进行充分的模拟测试
3. **监控仓位**：密切关注仪表盘中的仓位信息
4. **设置止损**：EA会自动设置止损，但要确保理解逻辑

### 技术要求
1. **网络稳定**：确保MT5连接稳定
2. **VPS推荐**：如需24小时运行，建议使用VPS
3. **定期检查**：定期检查EA运行状态
4. **备份设置**：保存好参数配置

### 故障排除
1. **EA不工作**：检查自动交易是否启用
2. **仪表盘不显示**：重新添加EA到图表
3. **信号不触发**：检查品种和时间框架设置
4. **编译错误**：检查MT5版本兼容性

## 联系支持

如果遇到技术问题：
1. 检查MT5终端的"专家"选项卡中的日志信息
2. 确认所有参数设置正确
3. 验证网络连接和服务器状态

## 免责声明

本EA仅供学习和研究使用。外汇交易存在高风险，可能导致资金损失。使用前请：
- 充分了解外汇交易风险
- 在模拟账户中充分测试
- 根据自身风险承受能力调整参数
- 不要投入超过承受能力的资金

交易有风险，投资需谨慎！
