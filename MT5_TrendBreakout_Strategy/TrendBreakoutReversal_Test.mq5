//+------------------------------------------------------------------+
//|                                TrendBreakoutReversal_Test.mq5   |
//|                                                测试版本          |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"
#property indicator_chart_window

// ================================
// 输入参数
// ================================
input int    EMA20_Period = 20;           // EMA20周期
input int    EMA50_Period = 50;           // EMA50周期
input int    RSI_Period = 14;             // RSI周期
input double Risk_Percent = 2.0;          // 风险比例(%)

// ================================
// 全局变量
// ================================
// 指标句柄
int ema20_handle, ema50_handle, rsi_handle, atr_handle;

// 指标数组
double ema20[], ema50[], rsi_values[], atr_values[];

// 监控变量
bool uptrend = false;
bool breakout_signal = false;
bool reversal_signal = false;
bool rsi_divergence = false;
double current_rsi = 0;
double highest_high = 0;
double volume_ratio = 0;

// 测试版仪表盘缓存
struct TestDashboardCache {
    bool uptrend_last;
    bool breakout_signal_last;
    bool reversal_signal_last;
    bool rsi_divergence_last;
    double current_rsi_last;
    double highest_high_last;
    double volume_ratio_last;
    double current_price_last;
    bool initialized;
} test_cache;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // 检查交易品种和时间框架
    Print("当前品种: ", Symbol());
    Print("当前时间框架: ", EnumToString(Period()));
    
    // 初始化指标
    ema20_handle = iMA(Symbol(), PERIOD_M3, EMA20_Period, 0, MODE_EMA, PRICE_CLOSE);
    ema50_handle = iMA(Symbol(), PERIOD_M3, EMA50_Period, 0, MODE_EMA, PRICE_CLOSE);
    rsi_handle = iRSI(Symbol(), PERIOD_M3, RSI_Period, PRICE_CLOSE);
    atr_handle = iATR(Symbol(), PERIOD_M3, 14);
    
    // 检查指标句柄
    if(ema20_handle == INVALID_HANDLE || ema50_handle == INVALID_HANDLE || 
       rsi_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE) {
        Print("指标初始化失败");
        return INIT_FAILED;
    }
    
    // 设置数组为时间序列
    ArraySetAsSeries(ema20, true);
    ArraySetAsSeries(ema50, true);
    ArraySetAsSeries(rsi_values, true);
    ArraySetAsSeries(atr_values, true);
    
    Print("测试指标初始化成功");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 释放指标句柄
    IndicatorRelease(ema20_handle);
    IndicatorRelease(ema50_handle);
    IndicatorRelease(rsi_handle);
    IndicatorRelease(atr_handle);
    
    // 清除图表对象
    ObjectsDeleteAll(0, "Test");
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // 检查新K线
    static datetime last_bar_time = 0;
    if(time[rates_total-1] == last_bar_time) return rates_total;
    last_bar_time = time[rates_total-1];
    
    // 获取指标数据
    if(!GetIndicatorData()) return rates_total;
    
    // 计算信号
    CalculateTestSignals(high, low, close, tick_volume);
    
    // 更新测试仪表盘
    UpdateTestDashboard();
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| 获取指标数据                                                      |
//+------------------------------------------------------------------+
bool GetIndicatorData()
{
    // 复制指标数据
    if(CopyBuffer(ema20_handle, 0, 0, 25, ema20) <= 0) return false;
    if(CopyBuffer(ema50_handle, 0, 0, 25, ema50) <= 0) return false;
    if(CopyBuffer(rsi_handle, 0, 0, 25, rsi_values) <= 0) return false;
    if(CopyBuffer(atr_handle, 0, 0, 25, atr_values) <= 0) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| 计算测试信号                                                      |
//+------------------------------------------------------------------+
void CalculateTestSignals(const double &high[], const double &low[], 
                         const double &close[], const long &tick_volume[])
{
    int rates_total = ArraySize(close);
    if(rates_total < 25) return;
    
    // 趋势判断
    double ema20_slope = ema20[0] - ema20[1];
    double ema50_slope = ema50[0] - ema50[1];
    uptrend = (ema20[0] > ema50[0] && ema20_slope > 0 && ema50_slope > 0);
    
    // 计算前高
    highest_high = high[rates_total-2];
    for(int i = rates_total-3; i >= MathMax(0, rates_total-21); i--) {
        if(high[i] > highest_high) highest_high = high[i];
    }
    
    // 成交量比率
    double vol_ma = 0;
    int vol_count = 0;
    for(int i = rates_total-2; i >= MathMax(0, rates_total-21); i--) {
        vol_ma += (double)tick_volume[i];
        vol_count++;
    }
    if(vol_count > 0) vol_ma = vol_ma / vol_count;
    
    volume_ratio = vol_ma > 0 ? (double)tick_volume[rates_total-1] / vol_ma : 0;
    
    // 突破信号
    bool price_breakout = close[rates_total-1] > highest_high;
    bool volume_confirm = (double)tick_volume[rates_total-1] > vol_ma;
    breakout_signal = uptrend && price_breakout && volume_confirm;
    
    // RSI
    current_rsi = rsi_values[0];

    // RSI底背离检测（修复版）
    double lowest_price_hist = low[rates_total-2];  // 从历史K线开始
    double lowest_rsi_hist = rsi_values[1];
    for(int i = 2; i <= MathMin(5, rates_total-2); i++) {
        if(rates_total-1-i >= 0) {
            if(low[rates_total-1-i] < lowest_price_hist) lowest_price_hist = low[rates_total-1-i];
            if(rsi_values[i] < lowest_rsi_hist) lowest_rsi_hist = rsi_values[i];
        }
    }

    bool price_making_lower = (low[rates_total-1] < lowest_price_hist);
    bool rsi_not_making_lower = (current_rsi > lowest_rsi_hist);
    rsi_divergence = price_making_lower && rsi_not_making_lower;

    // 反转信号（包含RSI背离）
    bool oversold = current_rsi < 30;
    bool downtrend = !uptrend;
    reversal_signal = downtrend && (oversold || rsi_divergence);

    // 调试输出
    if(rsi_divergence) {
        Print("测试版检测到RSI背离 - 当前价格:", low[rates_total-1], " 历史最低:", lowest_price_hist,
              " 当前RSI:", current_rsi, " 历史最低RSI:", lowest_rsi_hist);
    }
}

//+------------------------------------------------------------------+
//| 更新测试仪表盘                                                    |
//+------------------------------------------------------------------+
void UpdateTestDashboard()
{
    // 检查是否需要更新
    if(!NeedsTestDashboardUpdate()) {
        return;
    }

    ObjectsDeleteAll(0, "Test");

    int x = 20, y = 50, row = 20;
    
    CreateTestLabel("Test_Title", x, y, "=== 策略测试监控 ===", clrYellow, 12);
    
    CreateTestLabel("Test_Trend", x, y + row * 1, 
                   "趋势: " + (uptrend ? "上升 ✓" : "下降/横盘 ✗"), 
                   uptrend ? clrLime : clrRed);
    
    CreateTestLabel("Test_HighestHigh", x, y + row * 2, 
                   "前高: " + DoubleToString(highest_high, _Digits), clrWhite);
    
    CreateTestLabel("Test_RSI", x, y + row * 3, 
                   "RSI: " + DoubleToString(current_rsi, 2), 
                   current_rsi < 30 ? clrLime : (current_rsi > 70 ? clrRed : clrWhite));
    
    CreateTestLabel("Test_VolumeRatio", x, y + row * 4, 
                   "成交量比: " + DoubleToString(volume_ratio, 2), 
                   volume_ratio > 1.0 ? clrLime : clrRed);
    
    CreateTestLabel("Test_BreakoutSignal", x, y + row * 5, 
                   "突破信号: " + (breakout_signal ? "触发 ✓" : "未触发 ✗"), 
                   breakout_signal ? clrYellow : clrGray);
    
    CreateTestLabel("Test_RSIDivergence", x, y + row * 6,
                   "RSI背离: " + (rsi_divergence ? "检测到 ✓" : "未检测到 ✗"),
                   rsi_divergence ? clrLime : clrRed);

    CreateTestLabel("Test_ReversalSignal", x, y + row * 7,
                   "反转信号: " + (reversal_signal ? "触发 ✓" : "未触发 ✗"),
                   reversal_signal ? clrYellow : clrGray);

    CreateTestLabel("Test_CurrentPrice", x, y + row * 8,
                   "当前价格: " + DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_BID), _Digits),
                   clrWhite);

    // 更新缓存
    UpdateTestDashboardCache();
}

//+------------------------------------------------------------------+
//| 检查测试仪表盘是否需要更新                                         |
//+------------------------------------------------------------------+
bool NeedsTestDashboardUpdate()
{
    if(!test_cache.initialized) {
        return true;
    }

    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    if(test_cache.uptrend_last != uptrend ||
       test_cache.breakout_signal_last != breakout_signal ||
       test_cache.reversal_signal_last != reversal_signal ||
       test_cache.rsi_divergence_last != rsi_divergence ||
       MathAbs(test_cache.current_rsi_last - current_rsi) > 0.1 ||
       MathAbs(test_cache.highest_high_last - highest_high) > _Point ||
       MathAbs(test_cache.volume_ratio_last - volume_ratio) > 0.01 ||
       MathAbs(test_cache.current_price_last - current_price) > _Point) {
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| 更新测试仪表盘缓存                                                |
//+------------------------------------------------------------------+
void UpdateTestDashboardCache()
{
    test_cache.uptrend_last = uptrend;
    test_cache.breakout_signal_last = breakout_signal;
    test_cache.reversal_signal_last = reversal_signal;
    test_cache.rsi_divergence_last = rsi_divergence;
    test_cache.current_rsi_last = current_rsi;
    test_cache.highest_high_last = highest_high;
    test_cache.volume_ratio_last = volume_ratio;
    test_cache.current_price_last = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    test_cache.initialized = true;
}

//+------------------------------------------------------------------+
//| 创建测试标签                                                      |
//+------------------------------------------------------------------+
void CreateTestLabel(string name, int x, int y, string text, color clr, int font_size = 10)
{
    ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}
